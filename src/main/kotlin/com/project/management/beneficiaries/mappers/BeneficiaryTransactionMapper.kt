package com.project.management.beneficiaries.mappers

import com.project.management.beneficiaries.requests.PostRequestBeneficiaryTransaction
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.users.models.User
import java.time.ZonedDateTime

fun PostRequestBeneficiaryTransaction.toEntity(
    beneficiaryId: Long?,
    user: User,
    version: Long
): BeneficiaryTransaction {
    // Validate the request with the beneficiaryId
    this.validate(beneficiaryId, user)

    return BeneficiaryTransaction(
        organizationId = user.organizationId,
        amount = amount.toBigDecimal(),
        amountPaid = amountPaid.toBigDecimal(),
        description = description,
        beneficiaryId = beneficiaryId,
        projectId = projectId,
        transactionDate = ZonedDateTime.parse(transactionDate),
        createdBy = user.id,
        updatedBy = user.id,
        version = version,
        createdByDetails = user
    )
}

fun PostRequestProjectExpense.toBeneficiaryTransaction(projectId: Long): PostRequestBeneficiaryTransaction {
    return PostRequestBeneficiaryTransaction(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

internal fun PostRequestProjectExpense.toEntity(
    projectId: Long,
    beneficiaryTransaction: BeneficiaryTransaction,
    user: User,
    version: Long
): ProjectExpenseEntity {
    return ProjectExpenseEntity(
        organizationId = user.organizationId,
        beneficiaryId = beneficiaryId,
        beneficiaryTransactionId = beneficiaryTransaction.id!!,
        termsGroupId = termsGroupId,
        termId = termId,
        projectId = projectId,
        createdBy = user.id,
        updatedBy = user.id,
        version = version
    )
}