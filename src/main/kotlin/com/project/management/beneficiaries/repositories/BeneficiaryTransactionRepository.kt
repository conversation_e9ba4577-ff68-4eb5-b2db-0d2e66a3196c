package com.project.management.beneficiaries.repositories

import com.project.management.beneficiaries.models.BeneficiaryTransaction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface BeneficiaryTransactionQueryRepository : JpaRepository<BeneficiaryTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<BeneficiaryTransaction>

    fun findAllByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): List<BeneficiaryTransaction>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): BeneficiaryTransaction?

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND beneficiary_id = :beneficiaryId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND beneficiary_id = :beneficiaryId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): BigDecimal
}

@Repository
interface BeneficiaryTransactionMutateRepository : JpaRepository<BeneficiaryTransaction, Long> {
    @Modifying
    @Query(
        value = "UPDATE beneficiary_transactions SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}