package com.project.management.customers.repositories

import com.project.management.customers.models.CustomerTransaction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface CustomerTransactionRepository : JpaRepository<CustomerTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<CustomerTransaction>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): CustomerTransaction?

    fun findAllByOrganizationIdAndCustomerId(
        organizationId: Long,
        customerId: Long
    ): List<CustomerTransaction>

    @Modifying
    @Query(
        value = "UPDATE customer_transactions SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM customer_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM customer_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM customer_transactions WHERE organization_id = :organizationId AND customer_id = :customerId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndCustomerId(organizationId: Long, customerId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM customer_transactions WHERE organization_id = :organizationId AND customer_id = :customerId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationIdAndCustomerId(organizationId: Long, customerId: Long): BigDecimal
}