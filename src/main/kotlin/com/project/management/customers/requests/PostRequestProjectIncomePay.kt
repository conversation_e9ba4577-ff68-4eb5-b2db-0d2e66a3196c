package com.project.management.customers.requests

import com.project.management.projects.requests.PatchRequestProjectIncomeAmount

class PostRequestProjectIncomePay (
    val amountPaid: Double,
    val version: Long,
) {
    fun toModifyAmount(amount: Double): PatchRequestProjectIncomeAmount {
        return PatchRequestProjectIncomeAmount(
            amount = amount,
            amountPaid = amountPaid,
            transactionVersion = version
        )
    }
}