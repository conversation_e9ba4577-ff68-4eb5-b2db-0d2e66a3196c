package com.project.management.customers.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.requests.CustomerTransactionRequestDto
import com.project.management.customers.models.CustomerTransaction
import com.project.management.customers.requests.PatchCustomerTransactionRequestDto
import com.project.management.customers.mappers.CustomerTransactionMapper
import com.project.management.customers.repositories.CustomerTransactionRepository
import com.project.management.customers.validators.CustomerTransactionsValidator
import com.project.management.customers.validators.CustomerValidator
import com.project.management.projects.services.ProjectIncomesService
import com.project.management.money.project.ProjectMutateMoneyService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
class CustomerTransactionService(
    private val customerTransactionsRepository: CustomerTransactionRepository,
    private val currentUser: CurrentUserConfig,
    private val customerTransactionMapper: CustomerTransactionMapper,
    private val customerValidator: CustomerValidator,
    private val customerTransactionsValidator: CustomerTransactionsValidator,
    private val projectMutateMoneyService: ProjectMutateMoneyService
) {

    @Autowired
    @Lazy
    private lateinit var projectIncomes: ProjectIncomesService

    fun getAll(): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customerTransactions =
            customerTransactionsRepository.findAllByOrganizationId(user.organizationId)
        return customerTransactions
    }

    fun getAllByCustomerId(customerId: Long): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId,
            user.organizationId
        )
        val customerTransactions =
            customerTransactionsRepository.findAllByOrganizationIdAndCustomerId(
                user.organizationId,
                customer.id!!
            )
        return customerTransactions
    }

    @Transactional
    fun create(transaction: CustomerTransactionRequestDto, customerId: Long): CustomerTransaction {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId,
            user.organizationId
        )
        val customerTransaction = customerTransactionMapper.toTransaction(
            customerTransactionRequestDto = transaction,
            customerId = customer.id!!,
            organizationId = user.organizationId,
            userId = user.id,
            createdByDetails = user,
            version = 1
        )
        val result = customerTransactionsRepository.save(customerTransaction)
        return result
    }

    @Transactional
    fun updateTransaction(
        request: PatchCustomerTransactionRequestDto,
        customerTransactionId: Long
    ): CustomerTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = customerTransactionsValidator.validateExistsByIdAndOrganization(
            customerTransactionId = customerTransactionId,
            organizationId = loggedInUser.organizationId
        )

        if (transaction.version != request.version) throw BusinessException.ConflictException()

        val updatedTransaction = transaction
        updatedTransaction.description = request.description ?: transaction.description
        updatedTransaction.updatedBy = loggedInUser.id!!


        return customerTransactionsRepository.save(updatedTransaction)
    }

    @Transactional
    fun delete(customerTransactionId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = customerTransactionsValidator.validateExistsByIdAndOrganization(
            customerTransactionId = customerTransactionId,
            organizationId = loggedInUser.organizationId
        )
        customerTransactionsRepository.deleteByIdAndOrganizationId(
            id = customerTransactionId,
            organizationId = loggedInUser.organizationId,
            updatedBy = loggedInUser.id!!
        )
    }
}

