package com.project.management.customers.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.models.Customer
import com.project.management.customers.models.CustomerTransaction
import com.project.management.customers.repositories.CustomerRepository
import com.project.management.customers.repositories.CustomerTransactionRepository
import org.springframework.stereotype.Component

@Component
class CustomerValidator(
    private val customerRepository: CustomerRepository
) {

    fun getAll(organizationId: Long): List<Customer> {
        return customerRepository.findAllByOrganizationId(organizationId)
    }

    fun validateCustomerExistsByIdAndOrganizationId(
        customerId: Long,
        organizationId: Long
    ): Customer {
        return customerRepository.findByIdAndOrganizationId(customerId, organizationId)
            ?: throw BusinessException.NotFoundException("Customer with id $customerId not found")
    }
}

@Component
class CustomerTransactionsValidator(
    private val customerTransactionRepository: CustomerTransactionRepository
) {

    fun findAllByOrganizationId(organizationId: Long): List<CustomerTransaction> {
        return customerTransactionRepository.findAllByOrganizationId(organizationId)
    }

    fun validateExistsByIdAndOrganization(
        customerTransactionId: Long,
        organizationId: Long
    ): CustomerTransaction {
        return customerTransactionRepository
            .findByIdAndOrganizationId(
                customerTransactionId,
                organizationId
            )
            ?: throw BusinessException.NotFoundException("Customer transaction with id $customerTransactionId not found")
    }
}