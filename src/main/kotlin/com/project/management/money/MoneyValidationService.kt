package com.project.management.money

import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.customers.validators.CustomerTransactionsValidator
import com.project.management.customers.validators.CustomerValidator
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.validators.OrganizationValidator
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectIncomeValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.services.BalanceTransactionService
import com.project.management.users.validators.UserValidator
import org.springframework.stereotype.Service

@Service
class MoneyValidationService(
    private val beneficiaries: BeneficiaryValidator,
    private val customers: CustomerValidator,
    private val beneficiaryTransactionValidator: BeneficiaryTransactionValidator,
    private val customerTransactionValidator: CustomerTransactionsValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val projectValidator: ProjectValidator,
    private val usersTransactions: BalanceTransactionService,
    private val users: UserValidator,
    private val organizationValidator: OrganizationValidator,
) {

    fun validate(organizationId: Long) {
        val organization = organizationValidator
            .validateOrganizationExistsById(organizationId)

        validateExpenses(organization)
        validateIncomes(organization)
        validateOrganizationBalance(organization)
    }

    private fun validateExpenses(organization: OrganizationEntity) {
        val users = usersTransactions.getAll()
        val userExpenses = users.filter { it.transactionTag.isProjectExpense() }.sumOf { it.amount }

        val projects = projectValidator.findAllByOrganizationId(organization.id!!)
        val projectsAmount = projects.sumOf { it.totalExpenses }
        val projectsPaid = projects.sumOf { it.totalPaidExpenses }

        val projectTransactions = projectExpenseValidator
            .findAllByOrganizationId(organization.id!!)
        val projectsTransactionsAmount = projectTransactions.sumOf { it.beneficiaryTransaction.amount }
        val projectsTransactionsPaid = projectTransactions.sumOf { it.beneficiaryTransaction.amountPaid }

        val beneficiary = beneficiaries.findAllByOrganizationId(organization.id!!)
        val beneficiariesAmount = beneficiary.sumOf { it.balanceAccumulator }
        val beneficiariesPaid = beneficiary.sumOf { it.paidAccumulator }

        val beneficiaryTransactions = beneficiaryTransactionValidator.findAllByOrganizationId(organization.id!!)
        val beneficiaryTransactionsAmount = beneficiaryTransactions.sumOf { it.amount }
        val beneficiaryTransactionsPaid = beneficiaryTransactions.sumOf { it.amountPaid }

        if (userExpenses.negate() != projectsTransactionsPaid) {
            throw IllegalStateException("User expenses do not match project expenses paid: $userExpenses != $projectsTransactionsPaid")
        }

        if (projectsTransactionsAmount != beneficiaryTransactionsAmount) {
            throw IllegalStateException("Projects amount does not match beneficiaries amount: $projectsTransactionsAmount != $beneficiaryTransactionsAmount")
        }

        if (projectsTransactionsPaid != beneficiaryTransactionsPaid) {
            throw IllegalStateException("Projects paid does not match beneficiaries paid: $projectsTransactionsPaid != $beneficiaryTransactionsPaid")
        }

        if (projectsTransactionsAmount != projectsAmount) {
            throw IllegalStateException("Projects amount does not match project expenses amount: $projectsTransactionsAmount != $projectsAmount")
        }

        if (projectsTransactionsPaid != projectsPaid) {
            throw IllegalStateException("Projects paid does not match project expenses paid: $projectsTransactionsPaid != $projectsPaid")
        }

        if (projectsTransactionsAmount != beneficiariesAmount) {
            throw IllegalStateException("Projects amount does not match beneficiaries amount: $projectsTransactionsAmount != $beneficiariesAmount")
        }

        if (projectsTransactionsPaid != beneficiariesPaid) {
            throw IllegalStateException("Projects paid does not match beneficiaries paid: $projectsTransactionsPaid != $beneficiariesPaid")
        }

        if (projectsTransactionsPaid != organization.totalPaidExpenses) {
            throw IllegalStateException("Projects paid does not match organization paid expenses: $projectsTransactionsPaid != ${organization.totalPaidExpenses}")
        }

        if (projectsTransactionsAmount != organization.totalExpenses) {
            throw IllegalStateException("Projects amount does not match organization expenses: $projectsTransactionsAmount != ${organization.totalExpenses}")
        }
    }

    private fun validateIncomes(organization: OrganizationEntity) {
        val projects = projectValidator.findAllByOrganizationId(organization.id!!)
        val projectsAmount = projects.sumOf { it.totalIncomes }
        val projectsPaid = projects.sumOf { it.totalPaidIncomes }

        val projectTransactions = projectIncomeValidator.findAllByOrganizationId(organization.id!!)
        val projectTransactionsAmount = projectTransactions.sumOf { it.customerTransaction.amount }
        val projectTransactionsPaid = projectTransactions.sumOf { it.customerTransaction.amountPaid }

        val customers = customers.getAll(organization.id!!)
        val customersAmount = customers.sumOf { it.balanceAccumulator }
        val customersPaid = customers.sumOf { it.paidAccumulator }

        val customerTransactions = customerTransactionValidator.findAllByOrganizationId(organization.id!!)
        val customerTransactionsAmount = customerTransactions.sumOf { it.amount }
        val customerTransactionsPaid = customerTransactions.sumOf { it.amountPaid }

        if (projectTransactionsAmount != customerTransactionsAmount) {
            throw IllegalStateException("Projects amount does not match customers amount: $projectTransactionsAmount != $customerTransactionsAmount")
        }

        if (projectTransactionsPaid != customerTransactionsPaid) {
            throw IllegalStateException("Projects paid does not match customers paid: $projectTransactionsPaid != $customerTransactionsPaid")
        }

        if (projectTransactionsAmount != projectsAmount) {
            throw IllegalStateException("Projects amount does not match project incomes amount: $projectTransactionsAmount != $projectsAmount")
        }

        if (projectTransactionsPaid != projectsPaid) {
            throw IllegalStateException("Projects paid does not match project incomes paid: $projectTransactionsPaid != $projectsPaid")
        }

        if (projectTransactionsAmount != customersAmount) {
            throw IllegalStateException("Projects amount does not match customers amount: $projectTransactionsAmount != $customersAmount")
        }

        if (projectTransactionsPaid != customersPaid) {
            throw IllegalStateException("Projects paid does not match customers paid: $projectTransactionsPaid != $customersPaid")
        }

        if (projectTransactionsPaid != organization.totalPaidIncomes) {
            throw IllegalStateException("Projects paid does not match organization paid incomes: $projectTransactionsPaid != ${organization.totalPaidIncomes}")
        }

        if (projectTransactionsAmount != organization.totalIncomes) {
            throw IllegalStateException("Projects amount does not match organization incomes: $projectTransactionsAmount != ${organization.totalIncomes}")
        }
    }

    private fun validateOrganizationBalance(organization: OrganizationEntity) {
        val users = users.findAll(organization.id!!).sumOf { it.balance.negate() }
        val calculated = (organization.capital + organization.totalPaidIncomes + users) - (organization.totalPaidExpenses)
        if (organization.availableBalance != calculated) {
            throw IllegalStateException("Organization available balance does not match calculated balance: ${organization.availableBalance} != $calculated")
        }
    }
}