package com.project.management.money.project.usecases

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.money.MoneyValidationService
import com.project.management.money.customer.CustomerMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.repositories.ProjectIncomeRepository
import com.project.management.projects.validators.ProjectIncomeValidator
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteProjectIncomeMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val customer: CustomerMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService,
    private val projectIncomeRepository: ProjectIncomeRepository,
    private val money: MoneyValidationService
) {

    @Transactional
    fun delete(projectIncomeId: Long) {
        val user = currentUser.getCurrentUser()

        // Get existing income for deletion
        val existingIncome = projectIncomeValidator.validateExistsByIdAndOrganization(
            projectIncomeId = projectIncomeId,
            organizationId = user.organizationId
        )

        projectScenarios(existingIncome)
        organizationScenario(existingIncome)

        // Delete customer transaction
        transactionScenario(existingIncome)

        money.validate(organizationId = user.organizationId)
    }

    private fun transactionScenario(income: ProjectIncomeEntity) {
        customer.projectIncomeDelete(
            customerId = income.customerId,
            transactionId = income.customerTransaction.id!!,
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    private fun organizationScenario(income: ProjectIncomeEntity) {
        organizationProjectsMoneyMutateService.decreaseIncomes(
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    private fun projectScenarios(income: ProjectIncomeEntity) {
        projectMoneyMutateService.projectIncomeDelete(income = income)
    }
}
