package com.project.management.money.project.usecases

import com.project.management.beneficiaries.models.toEntity
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService

import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateProjectExpenseMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiary: BeneficiaryMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    @Transactional
    fun update(request: PatchRequestProjectExpenseAmount, projectExpenseId: Long): ProjectExpense {
        val user = currentUser.getCurrentUser()

        // Get existing expense for comparison
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = user.organizationId
        )

        validate(request, user, expense)
        userScenario(expense = expense, request = request)
        projectScenarios(request, expense)
        organizationScenario(expense = expense, request = request)

        transactionScenario(request, expense)

        return expense
    }

    private fun userScenario(expense: ProjectExpense, request: PatchRequestProjectExpenseAmount) {
        userMoneyMutateService.projectExpenseModify(request = request, expense = expense)
    }

    private fun transactionScenario(
        request: PatchRequestProjectExpenseAmount,
        existingExpense: ProjectExpense
    ) {
        beneficiary.projectExpenseModify(
            transaction = existingExpense.beneficiaryTransaction.toEntity(),
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun organizationScenario(expense: ProjectExpense, request: PatchRequestProjectExpenseAmount) {
        organizationProjectsMoneyMutateService.modifyExpenses(
            oldAmount = expense.beneficiaryTransaction.amount,
            oldAmountPaid = expense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun projectScenarios(
        request: PatchRequestProjectExpenseAmount,
        existingExpense: ProjectExpense
    ) {
        projectMoneyMutateService.projectExpenseModify(
            projectId = existingExpense.projectId,
            oldAmount = existingExpense.beneficiaryTransaction.amount,
            oldAmountPaid = existingExpense.beneficiaryTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun validate(
        request: PatchRequestProjectExpenseAmount,
        user: User,
        existingExpense: ProjectExpense
    ) {
        // Check admin permission
        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        // Check version conflicts
        if (existingExpense.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Project expense version conflict.")
        }

        if (existingExpense.beneficiaryTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Transaction version conflict.")
        }

        // Validate amount constraints
        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        // Validate project exists
        projectValidator.validateExistsByIdAndOrganizationId(
            projectId = existingExpense.projectId,
            organizationId = user.organizationId
        )

        // Validate beneficiary exists if provided
        existingExpense.beneficiaryId?.let { beneficiaryId ->
            beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
                beneficiaryId = beneficiaryId,
                organizationId = user.organizationId
            )
        }
    }
}
