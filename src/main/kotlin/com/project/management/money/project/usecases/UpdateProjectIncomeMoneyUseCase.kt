package com.project.management.money.project.usecases

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.customers.models.toEntity
import com.project.management.customers.requests.PostRequestProjectIncomePay
import com.project.management.customers.validators.CustomerValidator
import com.project.management.money.customer.CustomerMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.requests.PatchRequestProjectIncomeAmount
import com.project.management.projects.validators.ProjectIncomeValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateProjectIncomeMoneyUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val customerValidator: CustomerValidator,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val customer: CustomerMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    fun pay(request: PostRequestProjectIncomePay, customerTransactionId: Long): ProjectIncomeEntity {
        val income = projectIncomeValidator.validateExistsByCustomerTransactionIdAndOrganization(
            customerTransactionId = customerTransactionId,
            organizationId = currentUser.getCurrentUser().organizationId
        )
        return update(
            request.toModifyAmount(income.customerTransaction.amount.toDouble()), income.id!!
        )
    }

    @Transactional
    fun update(
        request: PatchRequestProjectIncomeAmount,
        projectIncomeId: Long
    ): ProjectIncomeEntity {
        val user = currentUser.getCurrentUser()

        // Get existing income for comparison
        val income = projectIncomeValidator.validateExistsByIdAndOrganization(
            projectIncomeId = projectIncomeId,
            organizationId = user.organizationId
        )

        validate(request, user, income)
        projectScenarios(request, income)
        organizationScenario(income = income, request = request)
        transactionScenario(request, income)

        return income
    }

    private fun transactionScenario(
        request: PatchRequestProjectIncomeAmount,
        existingIncome: ProjectIncomeEntity
    ) {
        customer.projectIncomeModify(
            transaction = existingIncome.customerTransaction.toEntity(),
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun organizationScenario(
        income: ProjectIncomeEntity,
        request: PatchRequestProjectIncomeAmount
    ) {
        organizationProjectsMoneyMutateService.modifyIncomes(
            oldAmount = income.customerTransaction.amount,
            oldAmountPaid = income.customerTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun projectScenarios(
        request: PatchRequestProjectIncomeAmount,
        existingIncome: ProjectIncomeEntity
    ) {
        projectMoneyMutateService.projectIncomeModify(
            projectId = existingIncome.projectId,
            oldAmount = existingIncome.customerTransaction.amount,
            oldAmountPaid = existingIncome.customerTransaction.amountPaid,
            newAmount = request.amount.toBigDecimal(),
            newAmountPaid = request.amountPaid.toBigDecimal()
        )
    }

    private fun validate(
        request: PatchRequestProjectIncomeAmount,
        user: User,
        existingIncome: ProjectIncomeEntity
    ) {
        // Check admin permission
        if (!user.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        // Check version conflicts
        if (existingIncome.customerTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException(message = "Project income version conflict. ${existingIncome.customerTransaction.version} != ${request.transactionVersion}")
        }
    }
}
