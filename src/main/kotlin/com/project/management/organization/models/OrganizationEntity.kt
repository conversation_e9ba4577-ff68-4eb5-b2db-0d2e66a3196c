package com.project.management.organization.models

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

@Entity
@Table(name = "organizations")
class OrganizationEntity(
    var address: String,
    var description: String,
    var email: String,
    var logoUrl: String?,
    var name: String,
    var organizationCode: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,
    var website: String,
    var photoUrl: String,

//    @OneToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "owner_id")
//    var owner: User,
    val ownerId: Long,

    var capital: BigDecimal,
    var availableBalance: BigDecimal,

    var totalExpenses: BigDecimal,
    var totalPaidExpenses: BigDecimal,
    var totalIncomes: BigDecimal,
    var totalPaidIncomes: BigDecimal,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
    var version: Long = 1,
) {
    @PreUpdate
    fun setLastUpdate() {
        version += 1
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}