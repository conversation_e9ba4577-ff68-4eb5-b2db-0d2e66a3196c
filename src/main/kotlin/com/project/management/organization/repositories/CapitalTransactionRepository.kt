package com.project.management.organization.repositories

import com.project.management.organization.models.CapitalTransaction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface CapitalTransactionRepository: JpaRepository<CapitalTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<CapitalTransaction>

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM capital_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal
}