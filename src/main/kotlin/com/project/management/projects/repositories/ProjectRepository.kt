package com.project.management.projects.repositories

import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.models.ProjectIncomeEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface ProjectRepository : JpaRepository<ProjectEntity, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<ProjectEntity>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectEntity?

    @Modifying
    @Query(
        value = "UPDATE projects SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}

@Repository
interface ProjectExpenseMutateRepository : JpaRepository<ProjectExpenseEntity, Long> {
    @Modifying
    @Query(
        value = "UPDATE project_expenses SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}

@Repository
interface ProjectExpenseQueryRepository : JpaRepository<ProjectExpense, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectExpense>

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long,
        pageable: Pageable
    ): Page<ProjectExpense>

    fun findAllByOrganizationIdAndBeneficiaryId(
        organizationId: Long,
        beneficiaryId: Long
    ): List<ProjectExpense>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectExpense?

    @Query(
        value = "SELECT * FROM project_expenses WHERE organization_id = :organizationId AND deleted IS NULL AND $createdAtLastWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedAtLastWeek(organizationId: Long): List<ProjectExpense>

    @Query(
        value = "SELECT * FROM project_expenses WHERE organization_id = :organizationId AND deleted IS NULL AND $createdThisWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectExpense>

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(bt.amount), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount_paid), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.project_id = :projectId AND pe.deleted IS NULL AND bt.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndProjectId(organizationId: Long, projectId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount_paid), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.project_id = :projectId AND pe.deleted IS NULL AND bt.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationIdAndProjectId(organizationId: Long, projectId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL AND $createdAtLastWeek",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdCreatedAtLastWeek(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(bt.amount), 0) FROM project_expenses pe JOIN beneficiary_transactions bt ON pe.beneficiary_transaction_id = bt.id WHERE pe.organization_id = :organizationId AND pe.deleted IS NULL AND bt.deleted IS NULL AND $createdThisWeek",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdCreatedThisWeek(organizationId: Long): BigDecimal
}

@Repository
interface ProjectIncomeRepository : JpaRepository<ProjectIncomeEntity, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectIncomeEntity>

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long
    ): List<ProjectIncomeEntity>

    fun findAllByOrganizationIdAndCustomerId(
        organizationId: Long,
        customerId: Long
    ): List<ProjectIncomeEntity>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectIncomeEntity?

    fun findByCustomerTransactionIdAndOrganizationId(customerTransactionId: Long, organizationId: Long): ProjectIncomeEntity?

    @Modifying
    @Query(
        value = "UPDATE project_incomes SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    @Query(
        value = "SELECT * FROM project_incomes WHERE organization_id = :organizationId AND deleted IS NULL AND $createdAtLastWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedAtLastWeek(organizationId: Long): List<ProjectIncomeEntity>

    @Query(
        value = "SELECT * FROM project_incomes WHERE organization_id = :organizationId AND deleted IS NULL AND $createdThisWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectIncomeEntity>

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(ct.amount), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.deleted IS NULL AND ct.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount_paid), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.deleted IS NULL AND ct.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.project_id = :projectId AND pi.deleted IS NULL AND ct.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndProjectId(organizationId: Long, projectId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount_paid), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.project_id = :projectId AND pi.deleted IS NULL AND ct.deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationIdAndProjectId(organizationId: Long, projectId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.deleted IS NULL AND ct.deleted IS NULL AND $createdAtLastWeek",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdCreatedAtLastWeek(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(ct.amount), 0) FROM project_incomes pi JOIN customer_transactions ct ON pi.customer_transaction_id = ct.id WHERE pi.organization_id = :organizationId AND pi.deleted IS NULL AND ct.deleted IS NULL AND $createdThisWeek",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdCreatedThisWeek(organizationId: Long): BigDecimal
}

internal const val createdAtLastWeek = "(created_at >= date_trunc('week', CURRENT_TIMESTAMP + interval '2 day' - interval '1 week')  - interval '2 day' and " +
        "created_at < date_trunc('week', CURRENT_TIMESTAMP + interval '2 day' ) - interval '2 day')"
internal const val createdThisWeek = "(created_at >= date_trunc('week', CURRENT_TIMESTAMP + interval '2 day') - interval '2 day')"