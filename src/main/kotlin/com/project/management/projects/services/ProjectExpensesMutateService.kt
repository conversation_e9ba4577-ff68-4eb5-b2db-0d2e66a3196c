package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PatchRequestProjectExpense
import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.money.project.ProjectMutateMoneyService
import com.project.management.projects.mappers.toEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProjectExpensesMutateService(
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransactionQueryService: BeneficiaryTransactionMutateService,
    private val projectService: ProjectMutateMoneyService,
    private val userMoneyMutateService: UserMoneyMutateService
) {

    @Transactional
    fun updateProjectExpense(
        request: PatchRequestProjectExpense,
        projectExpenseId: Long
    ): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        ).toEntity()

        // Only validate term and terms group if they are provided
        request.termId?.let {
            termValidator.validateTermExistsByIdAndOrganizationId(
                termId = request.termId,
                organizationId = loggedInUser.organizationId
            )
        }

        request.termsGroupId?.let {
            termsGroupValidator.validateExistsByIdAndOrganization(
                termsGroupId = request.termsGroupId,
                organizationId = loggedInUser.organizationId
            )
        }

        return projectExpenseRepository.save(expense.copy(
            termId = request.termId ?: expense.termId,
            termsGroupId = request.termsGroupId ?: expense.termsGroupId,
            updatedBy = loggedInUser.id,
            version = request.version
        ))
    }

    @Transactional
    fun createProjectExpense(
        request: PostRequestProjectExpense,
        projectId: Long
    ): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()

        // Validate project is required
        projectValidator.validateExistsByIdAndOrganization(
            projectId = projectId,
            organizationId = loggedInUser.organizationId
        )

        // Only validate optional fields if they are provided
        request.beneficiaryId?.let {
            beneficiaryValidator.validateExistsByIdAndOrganization(
                beneficiaryId = it,
                organizationId = loggedInUser.organizationId
            )
        }

        request.termsGroupId?.let {
            termsGroupValidator.validateExistsByIdAndOrganization(
                termsGroupId = it,
                organizationId = loggedInUser.organizationId
            )
        }

        request.termId?.let {
            termValidator.validateTermExistsByIdAndOrganizationId(
                termId = it,
                organizationId = loggedInUser.organizationId
            )
        }

        // Create beneficiary transaction
        val beneficiaryTransaction = beneficiaryTransactionQueryService.createBeneficiaryTransaction(
            amount = request.amount,
            amountPaid = request.amountPaid,
            description = request.description,
            transactionDate = request.transactionDate,
            beneficiaryId = request.beneficiaryId,
            createdBy = loggedInUser.id
        )

        return projectExpenseRepository.save(
            ProjectExpenseEntity(
                organizationId = loggedInUser.organizationId,
                beneficiaryId = request.beneficiaryId,
                beneficiaryTransactionId = beneficiaryTransaction.id!!,
                termsGroupId = request.termsGroupId,
                termId = request.termId,
                projectId = projectId,
                createdBy = loggedInUser.id,
                updatedBy = loggedInUser.id,
                version = 1
            )
        )
    }
}

