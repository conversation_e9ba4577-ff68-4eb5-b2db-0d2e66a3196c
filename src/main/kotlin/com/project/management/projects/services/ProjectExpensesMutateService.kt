package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.projects.requests.PatchRequestProjectExpense
import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.money.project.ProjectMutateMoneyService
import com.project.management.projects.mappers.toEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProjectExpensesMutateService(
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransactionQueryService: BeneficiaryTransactionMutateService,
    private val projectService: ProjectMutateMoneyService,
    private val userMoneyMutateService: UserMoneyMutateService
) {

    @Transactional
    fun updateProjectExpense(
        request: PatchRequestProjectExpense,
        projectExpenseId: Long
    ): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        ).toEntity()

        // Check version conflict early
        if (expense.version != request.version) {
            throw BusinessException.ConflictException()
        }

        // Validate terms consistency if provided
        validateTermsConsistency(request, loggedInUser.organizationId)

        val updatedExpense = expense.copy(
            termsGroupId = request.termsGroupId ?: expense.termsGroupId,
            termId = request.termId ?: expense.termId,
            updatedBy = loggedInUser.id!!
        )
        val projectExpenseEntity = projectExpenseRepository.save(updatedExpense)

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseEntity.id!!,
            organizationId = loggedInUser.organizationId
        )
    }

    private fun validateTermsConsistency(request: PatchRequestProjectExpense, organizationId: Long) {
        // Early return if no terms provided
        if (request.termId == null && request.termsGroupId == null) return

        // Validate that both terms are provided together
        if (request.termId != null && request.termsGroupId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together.")
        }

        if (request.termsGroupId != null && request.termId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together.")
        }

        // Validate terms when both are provided
        val term = termValidator.validateTermExistsByIdAndOrganizationId(
            termId = request.termId!!,
            organizationId = organizationId
        )
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId = request.termsGroupId!!,
            organizationId = organizationId
        )

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.BadRequestException(message = "Term and TermsGroup do not match.")
        }
    }
}