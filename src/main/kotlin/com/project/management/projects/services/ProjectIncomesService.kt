package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.requests.PatchRequestProjectIncomeAmount
import com.project.management.customers.mappers.CustomerMapper
import com.project.management.customers.services.CustomerTransactionService
import com.project.management.projects.mappers.ProjectIncomeMapper
import com.project.management.projects.repositories.ProjectIncomeRepository
import com.project.management.customers.validators.CustomerValidator
import com.project.management.money.project.ProjectMutateMoneyService
import com.project.management.projects.validators.ProjectIncomeValidator
import com.project.management.projects.validators.ProjectValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProjectIncomesService(
    private val projectIncomeRepository: ProjectIncomeRepository,
    private val currentUser: CurrentUserConfig,
    private val projectIncomeMapper: ProjectIncomeMapper,
    private val customerMapper: CustomerMapper,
    private val projectValidator: ProjectValidator,
    private val customerValidator: CustomerValidator,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val customerTransactionService: CustomerTransactionService,
    private val projectMutateMoneyService: ProjectMutateMoneyService,
) {

    fun getAll(): List<ProjectIncomeEntity> {
        val user = currentUser.getCurrentUser()
        val projectIncomes = projectIncomeRepository.findAllByOrganizationId(user.organizationId)
        return projectIncomes
    }

    fun getAllByProjectId(projectId: Long): List<ProjectIncomeEntity> {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
        val projectIncomes = projectIncomeRepository.findAllByOrganizationIdAndProjectId(
            user.organizationId,
            projectId
        )
        return projectIncomes
    }

    fun getAllIncomesByCustomerId(customerId: Long): List<ProjectIncomeEntity> {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = customerId,
            organizationId = user.organizationId
        )
        val projectIncomes = projectIncomeRepository.findAllByOrganizationIdAndCustomerId(
            organizationId = user.organizationId,
            customerId = customerId
        )
        return projectIncomes
    }
}

