package com.project.management.projects.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.repositories.ProjectExpenseQueryRepository
import com.project.management.projects.repositories.ProjectIncomeRepository
import com.project.management.projects.repositories.ProjectRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Component

@Component
class ProjectValidator(
    private val projectRepository: ProjectRepository
) {

    fun findAllByOrganizationId(organizationId: Long): List<ProjectEntity> {
        return projectRepository.findAllByOrganizationId(organizationId)
    }

    fun validateExistsByIdAndOrganizationId(projectId: Long, organizationId: Long): ProjectEntity {
        return projectRepository.findByIdAndOrganizationId(projectId, organizationId)
            ?: throw BusinessException.NotFoundException("Project with id $projectId not found")
    }
}

@Component
class ProjectIncomeValidator(
    private val projectIncomeRepository: ProjectIncomeRepository
) {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectIncomeEntity> {
        return projectIncomeRepository.findAllByOrganizationId(organizationId)
    }

    fun validateExistsByIdAndOrganization(
        projectIncomeId: Long,
        organizationId: Long
    ): ProjectIncomeEntity {
        return projectIncomeRepository.findByIdAndOrganizationId(projectIncomeId, organizationId)
            ?: throw BusinessException.NotFoundException("Project income with id $projectIncomeId not found")
    }

    fun validateExistsByCustomerTransactionIdAndOrganization(
        customerTransactionId: Long,
        organizationId: Long
    ): ProjectIncomeEntity {
        return projectIncomeRepository
            .findByCustomerTransactionIdAndOrganizationId(organizationId, customerTransactionId)
            ?: throw BusinessException.NotFoundException("Project income with customer transaction id $customerTransactionId not found")
    }
}

@Component
class ProjectExpenseValidator(
    private val projectExpenseRepository: ProjectExpenseQueryRepository
) {

    fun findAllByOrganizationId(organizationId: Long): List<ProjectExpense> {
        return projectExpenseRepository.findAllByOrganizationId(organizationId)
    }

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long,
        pageable: Pageable
    ): Page<ProjectExpense> {
        return projectExpenseRepository
            .findAllByOrganizationIdAndProjectId(
                organizationId = organizationId,
                projectId = projectId,
                pageable = pageable
            )
    }

    fun findAllByOrganizationIdAndBeneficiaryId(
        organizationId: Long,
        beneficiaryId: Long
    ): List<ProjectExpense> {
        return projectExpenseRepository.findAllByOrganizationIdAndBeneficiaryId(
            organizationId = organizationId,
            beneficiaryId = beneficiaryId
        )
    }

    fun findAllByOrganizationIdCreatedAtLastWeek(organizationId: Long): List<ProjectExpense> {
        return projectExpenseRepository.findAllByOrganizationIdCreatedAtLastWeek(organizationId)
    }

    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectExpense> {
        return projectExpenseRepository.findAllByOrganizationIdCreatedThisWeek(organizationId)
    }

    fun validateExistsByIdAndOrganization(
        projectExpenseId: Long,
        organizationId: Long
    ): ProjectExpense {
        return projectExpenseRepository.findByIdAndOrganizationId(projectExpenseId, organizationId)
            ?: throw BusinessException.NotFoundException("Project expense with id $projectExpenseId not found")
    }
}